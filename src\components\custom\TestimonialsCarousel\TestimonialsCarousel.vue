<template>
	<Carousel class="mx-auto w-full md:w-5/6 xl:w-3/5" :opts="carouselProps">
		<CarouselContent>
			<CarouselItem v-for="(testimonial, key) in testimonials" :key class="">
				<div
					data-name="testimonial-wrapper"
					class="flex w-full flex-col gap-8 rounded-2xl border border-b-4 border-primary bg-white p-4 md:p-8 lg:flex-row lg:items-center"
				>
					<div data-name="img" class="overflow-hidden rounded-xl lg:max-w-xs lg:shrink-0">
						<slot :name="testimonial.slot"></slot>
					</div>
					<figure data-name="testimonial">
						<div class="mb-6 inline-block rounded-full bg-primary p-4 text-white">
							<Quote :size="32" />
						</div>
						<blockquote class="mb-4 text-2xl md:text-3xl">
							<p class="italic">“{{ testimonial.quote }}”</p>
						</blockquote>
						<figcaption
							class="flex flex-col divide-gray-700 md:flex-row md:items-center md:divide-x-2"
						>
							<cite class="font-bold not-italic md:pr-4">Bạn {{ testimonial.name }}</cite>
							<cite class="text-gray-700 md:pl-4">{{ testimonial.school }}</cite>
						</figcaption>
					</figure>
				</div>
			</CarouselItem>
		</CarouselContent>
		<CarouselPrevious data-name="prev" />
		<CarouselNext data-name="next" />
	</Carousel>
</template>

<style scoped>
@media not all and (min-width: 768px) {
	button[data-name="prev"],
	button[data-name="next"] {
		width: 3rem;
		height: 3rem;
		top: calc(5rem + (100vw - 6rem) * 4 / 3);
		left: unset;
		--tw-translate-y: -50%;
	}
	button[data-name="prev"] {
		right: 5rem;
	}
	button[data-name="next"] {
		right: 1rem;
	}
	button[data-name="prev"] :deep(svg),
	button[data-name="next"] :deep(svg) {
		width: 1.5rem;
		height: 1.5rem;
	}
}
</style>

<script setup lang="ts">
import {
	Carousel,
	CarouselContent,
	CarouselItem,
	CarouselNext,
	CarouselPrevious
} from "@/components/ui/carousel";
import type { CarouselProps } from "@/components/ui/carousel/interface";

import { testimonials } from ".";
import { Quote } from "lucide-vue-next";

const carouselProps = {
	align: "start",
	loop: true
} satisfies CarouselProps["opts"];
</script>

---
import { Image } from "astro:assets";
import { TextStroke } from "@/components/astro";
import { Slider, SliderItem } from "@/components/astro/Slider";

import { partners } from "@/components/astro/partners";
import { BlogCarousel } from "@/components/custom/BlogCarousel";

const sliderItemWidth = 304;
const sliderItemHeight = 200;
---

<section data-name="partners" class="py-16">
	<h2
		class="container mb-4 flex flex-col items-center text-center font-bold"
		data-name="section-title"
	>
		<span class="mb-2 flex justify-center gap-2 text-3xl/snug md:text-4xl/snug">
			<TextStroke
				as="span"
				class="inline-block"
				text="StudyCare "
				color="hsl(var(--secondary))"
				strokeColor="white"
			/>
			<TextStroke
				as="span"
				class="inline-block"
				text="Education "
				color="hsl(var(--primary))"
				strokeColor="white"
			/>
		</span>
		<TextStroke
			as="span"
			text="Là đối tác của nhiều tổ chức gi<PERSON>o dục trong và ngoài nước"
			class="inline-block max-w-[19rem] text-2xl/snug uppercase md:max-w-2xl md:text-4xl/snug lg:max-w-4xl"
			color="white"
			strokeColor="hsl(var(--secondary))"
			strokeWidth={3}
		/>
	</h2>
	<div data-name="section-content" class="mb-12">
		<Slider
			quantity={partners.length}
			width={sliderItemWidth}
			height={sliderItemHeight}
			duration={20}
		>
			{
				partners.map((partner, index) => (
					<SliderItem position={index}>
						<Image
							src={partner.src}
							alt={partner.alt}
							width={sliderItemWidth}
							height={sliderItemHeight}
						/>
					</SliderItem>
				))
			}
		</Slider>
		<Slider
			quantity={partners.length}
			width={sliderItemWidth}
			height={sliderItemHeight}
			duration={20}
			reverse={true}
		>
			{
				partners.map((partner, index) => (
					<SliderItem position={index}>
						<Image
							src={partner.src}
							alt={partner.alt}
							width={sliderItemWidth}
							height={sliderItemHeight}
						/>
					</SliderItem>
				))
			}
		</Slider>
	</div>
	<div data-name="section-content" class="container">
		<h2
			class="mb-8 flex flex-col items-center gap-1 text-center text-[1.15rem]/normal font-bold sm:text-[1.4rem]/normal md:gap-2 md:text-4xl/snug"
		>
			<span class="flex justify-center gap-2">
				<TextStroke
					as="span"
					class="inline-block text-3xl/snug md:text-4xl/snug"
					text="StudyCare "
					color="hsl(var(--secondary))"
					strokeColor="white"
				/>
				<TextStroke
					as="span"
					class="inline-block text-3xl/snug md:text-4xl/snug"
					text="Education "
					color="hsl(var(--primary))"
					strokeColor="white"
				/>
			</span>
			<TextStroke
				as="span"
				text="Cùng các dự án giáo dục, "
				class="inline-block uppercase"
				color="white"
				strokeColor="hsl(var(--primary))"
				strokeWidth={3}
			/>
			<TextStroke
				as="span"
				text="mang lại nhiều giá trị, đóng góp cho cộng đồng, "
				class="inline-block max-w-full uppercase max-[681px]:max-w-[22rem] max-sm:max-w-72 md:max-w-2xl xl:max-w-full"
				color="white"
				strokeColor="hsl(var(--primary))"
				strokeWidth={3}
			/>
			<TextStroke
				as="span"
				text="thể hiện trách nhiệm xã hội"
				class="inline-block uppercase"
				color="white"
				strokeColor="hsl(var(--primary))"
				strokeWidth={3}
			/>
		</h2>
		<div data-name="blog-posts">
			<BlogCarousel client:load />
		</div>
	</div>
</section>

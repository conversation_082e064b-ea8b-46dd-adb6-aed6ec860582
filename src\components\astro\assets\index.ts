import type { ImageOutputFormat } from "astro";
import type { HTMLAttributes } from "astro/types";
import type { LocalImageProps, RemoteImageProps } from "astro:assets";

export type ImageProps = LocalImageProps | RemoteImageProps;
export type PictureProps = ImageProps & {
	formats?: ImageOutputFormat[];
	fallbackFormat?: ImageOutputFormat;
	pictureAttributes?: HTMLAttributes<"picture">;
};

export { default as PictureBackground } from "./PictureBackground.astro";
export { default as Picture } from "./Picture.astro";
export { default as Image } from "./Image.astro";
export { getImage } from "astro:assets";

<template>
	<Dialog>
		<DialogTrigger as-child>
			<slot></slot>
		</DialogTrigger>
		<DialogContent class="w-4/5 max-w-2xl rounded-xl border-2 border-b-4 border-primary">
			<DialogTitle class="mb-6 text-center text-2xl font-bold text-primary">
				<PERSON><PERSON><PERSON> ký lớp học trải nghiệm và tư vấn miễn phí
			</DialogTitle>
			<ContactForm :form-i-d="props.formID" :id="props.id" />
		</DialogContent>
	</Dialog>
</template>

<script setup lang="ts">
import type { HTMLAttributes } from "vue";
import { Dialog, DialogContent, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { ContactForm } from "@/components/custom/ContactForm";

interface Props {
	formID: string;
	id?: HTMLAttributes["id"];
}

const props = defineProps<Props>();
</script>

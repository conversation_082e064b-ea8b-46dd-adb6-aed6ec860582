import { type VariantProps, cva } from "class-variance-authority";
export { default as <PERSON><PERSON> } from "./Button.vue";

export const buttonVariants = cva(
	"inline-flex items-center justify-center whitespace-nowrap rounded-xl border border-b-4 text-lg font-bold ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
	{
		variants: {
			variant: {
				default: "bg-primary text-primary-foreground border-yellow-300 hover:bg-primary/90",
				primary: "bg-white text-primary border-primary hover:bg-neutral-100",
				// destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
				outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
				secondary: "bg-white text-secondary border-secondary hover:bg-neutral-100",
				accent: "bg-accent text-accent-foreground border-neutral-400 hover:bg-neutral-200"
				// ghost: "hover:bg-accent hover:text-accent-foreground",
				// link: "text-primary underline-offset-4 hover:underline"
			},
			size: {
				default: "h-12 px-6",
				// xs: "h-7 px-2",
				// sm: "h-9 px-3",
				lg: "h-14 px-8"
				// icon: "h-10 w-10"
			}
		},
		defaultVariants: {
			variant: "default",
			size: "default"
		}
	}
);

export type ButtonVariants = VariantProps<typeof buttonVariants>;

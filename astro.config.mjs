// @ts-check
import { defineConfig } from "astro/config";
import vue from "@astrojs/vue";
import tailwind from "@astrojs/tailwind";
import node from "@astrojs/node";
import partytown from "@astrojs/partytown";
import sitemap from "@astrojs/sitemap";
import robotsTxt from "astro-robots-txt";

// https://astro.build/config
export default defineConfig({
	site: "https://landing.studycare.edu.vn",
	integrations: [
		vue({ appEntrypoint: "./src/pages/_app" }),
		tailwind({ applyBaseStyles: false }),
		partytown(),
		sitemap(),
		robotsTxt()
	],
	output: "hybrid",
	adapter: node({
		mode: "standalone"
	}),
	image: {
		domains: ["studycare.edu.vn"]
	},
	redirects: {
		"/": "/giang-day-chuong-trinh-cambridge-tai-studycare-education"
	}
});

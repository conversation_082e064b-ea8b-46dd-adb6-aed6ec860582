export function sleep(ms: number) {
	return new Promise((resolve) => setTimeout(resolve, ms));
}

export function processText(inputText: string) {
	const regex = /--|-/g;
	const escapedHyphen = "--";

	const html = (): string => {
		return inputText.replace(regex, (match) => {
			if (match === escapedHyphen) {
				return "-";
			} else {
				return "&nbsp;";
			}
		});
	};

	const unicode = (): string => {
		return inputText.replace(regex, (match) => {
			if (match === escapedHyphen) {
				return "-";
			} else {
				return "\u00A0";
			}
		});
	};

	return {
		html,
		unicode
	};
}

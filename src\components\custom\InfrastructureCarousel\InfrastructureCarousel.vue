<template>
	<Carousel
		class="mx-auto mb-2 w-4/5 max-w-[80%]"
		:opts="{
			loop: true
		}"
		@init-api="(val) => (emblaMainApi = val)"
	>
		<CarouselContent>
			<CarouselItem v-for="(infrastructure, key) in infrastructures" :key>
				<div
					class="overflow-hidden rounded-xl border-2 border-b-4 border-primary bg-white shadow-xl md:rounded-2xl"
				>
					<slot :name="infrastructure.slot"></slot>
				</div>
			</CarouselItem>
		</CarouselContent>
		<CarouselPrevious />
		<CarouselNext />
	</Carousel>

	<Carousel
		class="mx-auto w-4/5"
		:opts="{
			align: 'start',
			loop: true
		}"
		@init-api="(val) => (emblaThumbnailApi = val)"
	>
		<CarouselContent class="-ml-1 gap-1">
			<CarouselItem
				v-for="(infrastructure, key) in infrastructures"
				:key
				class="basis-1/4 cursor-pointer pl-1"
				@click="onThumbClick(key)"
			>
				<div
					:class="
						cn(
							'overflow-hidden rounded-xl border-2 border-b-4 border-primary bg-white shadow-xl md:rounded-2xl',
							key === selectedIndex ? '' : 'opacity-50'
						)
					"
				>
					<slot :name="infrastructure.thumbnailSlot"></slot>
				</div>
			</CarouselItem>
		</CarouselContent>
	</Carousel>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { watchOnce } from "@vueuse/core";
import {
	Carousel,
	type CarouselApi,
	CarouselContent,
	CarouselItem,
	CarouselNext,
	CarouselPrevious
} from "@/components/ui/carousel";
import { cn } from "@/lib/utils";

import { infrastructures } from ".";

const emblaMainApi = ref<CarouselApi>();
const emblaThumbnailApi = ref<CarouselApi>();
const selectedIndex = ref(0);

function onSelect() {
	if (!emblaMainApi.value || !emblaThumbnailApi.value) return;
	selectedIndex.value = emblaMainApi.value.selectedScrollSnap();
	emblaThumbnailApi.value.scrollTo(emblaMainApi.value.selectedScrollSnap());
}

function onThumbClick(index: number) {
	if (!emblaMainApi.value || !emblaThumbnailApi.value) return;
	emblaMainApi.value.scrollTo(index);
}

watchOnce(emblaMainApi, (emblaMainApi) => {
	if (!emblaMainApi) return;

	onSelect();
	emblaMainApi.on("select", onSelect);
	emblaMainApi.on("reInit", onSelect);
});
</script>

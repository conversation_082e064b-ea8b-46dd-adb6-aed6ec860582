---
import { Picture, Image } from "@/components/astro/assets";
import { TextStroke } from "@/components/astro";
import { processText } from "@/lib/features";

import whyGEDImg from "@/assets/why-ged.jpg";
import graduatedIcon from "@/assets/graduated.png";

const whyGED = [
	{
		title: "Vượt-cấp vào đại-học, tiết-kiệm 3-năm",
		content:
			"Không cần phải học 3 năm THPT, bạn chỉ cần học GED trong vài tháng để có ngay tấm bằng tương-đ<PERSON>ơng, mở cánh-cửa vào đại-học sớm."
	},
	{
		title: "Tiết-kiệm chi-phí đáng-kể",
		content:
			"Lộ-trình ngắn-gọn giúp tiết-kiệm thời-gian, giảm mạnh chi-phí từ vài trăm triệu đến hàng tỷ-đồng."
	},
	{
		title: "Bằng THPT quốc-tế, công-nhận toàn-cầu",
		content:
			"Chứng-chỉ GED tương-đương bằng THPT Mỹ, được các trường đại-học danh-tiếng khắp thế-giới công-nhận."
	},
	{
		title: "Cơ-hội nghề-nghiệp & thu-nhập cao-hơn",
		content:
			"Sở-hữu chứng-chỉ GED giúp bạn cạnh-tranh mạnh-mẽ trên thị-trường lao-động và nâng-cao thu-nhập."
	}
];
---

<section data-name="why-cambridge" class="py-16">
	<div class="container">
		<div data-name="section-title" class="mb-12 flex flex-col items-center gap-4">
			<TextStroke
				as="h2"
				text="Tại-sao nên chọn thi GED?"
				class="text-center text-3xl/tight font-bold uppercase md:text-4xl/tight"
				color="white"
				strokeColor="hsl(var(--primary))"
				strokeWidth={3}
			/>
			<Image
				src="/site/ged-logo.svg"
				alt="Logo GED"
				width={130}
				height={74}
				class="object-contain"
			/>
		</div>
		<div
			data-name="section-content"
			class="flex flex-col items-center justify-center gap-16 lg:flex-row lg:gap-24"
		>
			<div data-name="content-left" class="w-full max-w-md lg:w-1/2">
				<Picture
					src={whyGEDImg}
					alt="Ảnh du học sinh"
					widths={[362, 448, 724, 896, 1086, 1344]}
					sizes={`(max-width: 425px) 362px, 448px`}
				/>
			</div>
			<div data-name="content-right" class="lg:w-1/2">
				<ul data-name="content-container" class="w-full">
					{
						whyGED.map((item) => (
							<li>
								<Picture
									src={graduatedIcon}
									alt="Icon tốt nghiệp"
									width={48}
									height={48}
									widths={[48, 96, 144]}
									sizes="48px"
								/>
								<div>
									<h3 set:html={processText(item.title).html()} />
									<p set:html={processText(item.content).html()} />
								</div>
							</li>
						))
					}
				</ul>
			</div>
		</div>
	</div>
</section>

<style>
	[data-name="content-container"] li {
		@apply flex gap-4 pb-4;
	}
	[data-name="content-container"] li:last-child {
		@apply pb-0;
	}
	[data-name="content-container"] li + li {
		@apply border-t-2 border-gray-100 pt-4;
	}
	[data-name="content-container"] picture {
		@apply h-12 w-12 shrink-0 pt-1;
	}
	[data-name="content-container"] h3 {
		@apply text-xl font-bold lg:text-2xl;
	}
	[data-name="content-container"] p {
		@apply text-base lg:text-lg;
	}
</style>

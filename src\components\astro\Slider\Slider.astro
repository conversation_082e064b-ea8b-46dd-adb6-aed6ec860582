---
import { cn } from "@/lib/utils";

interface Props {
	quantity: number;
	as?: string;
	width?: number;
	height?: number;
	duration?: number;
	reverse?: boolean;
}

const {
	quantity,
	as = "div",
	width = 100,
	height = 50,
	duration = 10,
	reverse = false
} = Astro.props;

const Element = as;
---

<Element
	data-name="slider"
	data-slider-reverse={reverse}
	class={cn("w-full overflow-hidden", Astro.props.class)}
	style={`--width: ${width}px; --height: ${height}px; --quantity: ${quantity}; --duration: ${duration}s;`}
>
	<ul data-name="list" class="relative flex w-full">
		<slot />
	</ul>
</Element>

<style is:inline>
	[data-name="slider"] {
		height: var(--height);
		mask-image: linear-gradient(to right, transparent, #000 10% 90%, transparent);
	}
	[data-name="slider"] [data-name="list"] {
		min-width: calc(var(--width) * var(--quantity));
	}
	[data-name="slider"] [data-name="list"] [data-name="item"] {
		width: var(--width);
		height: var(--height);
		animation: autoRun var(--duration) linear infinite;
		transition: filter 0.5s;
		animation-delay: calc(
			(var(--duration) / var(--quantity)) * (var(--position) - 1) - var(--duration)
		) !important;
	}

	@keyframes autoRun {
		from {
			left: 100%;
		}
		to {
			left: calc(var(--width) * -1);
		}
	}

	[data-name="slider"]:hover [data-name="item"] {
		animation-play-state: paused !important;
		filter: grayscale(1);
	}
	[data-name="slider"] [data-name="item"]:hover {
		filter: grayscale(0);
	}
	[data-name="slider"][data-slider-reverse] [data-name="item"] {
		animation: reversePlay var(--duration) linear infinite;
	}

	@keyframes reversePlay {
		from {
			left: calc(var(--width) * -1);
		}
		to {
			left: 100%;
		}
	}
</style>

---
import { Image } from "@/components/astro/assets";
import { MapPin, PhoneCall, Mail } from "lucide-vue-next";
import { <PERSON>alo } from "@/components/icons";
import { processText } from "@/lib/features";

import { IconBox } from "@/components/astro";
---

<footer id="footer" class="bg-sky-950 py-16">
	<div class="container text-white">
		<div data-name="first-row" class="mb-16 flex flex-col gap-16 md:flex-row">
			<div data-name="info" class="w-full md:w-1/2">
				<div data-name="logo" class="mb-6">
					<a
						href="/giang-day-chuong-trinh-cambridge-tai-studycare-education"
						aria-current="page"
						class="inline-block"
					>
						<Image
							src="/site/web-logo.svg"
							alt="StudyCare Education Logo"
							width={170}
							height={120}
						/>
					</a>
				</div>
				<div data-name="description">
					<h2 class="mb-2 text-xl font-bold">StudyCare Education Group</h2>
					<p
						set:html={processText(
							`Đơn-vị hàng-đầu cung-cấp các giải-pháp giáo-dục toàn-diện, hỗ-trợ học-viên trong hành-trình học-tập và phát-triển bản-thân.`
						).html()}
					/>
				</div>
			</div>
			<div data-name="contacts">
				<h2 class="mb-2 text-2xl font-bold">Thông tin liên hệ</h2>
				<ul class="flex flex-col gap-2">
					<li>
						<IconBox title="Headquarter" content="72/53 Nguyễn Văn Thương, P. 25, Q. Bình Thạnh">
							<MapPin size={20} />
						</IconBox>
					</li>
					<li>
						<IconBox title="StudyCare Building" content="T4-35, The Manhattan, Vinhomes Grand Park">
							<MapPin size={20} />
						</IconBox>
					</li>
					<li>
						<IconBox title="Zalo" content="************">
							<Zalo size={20} />
						</IconBox>
					</li>
					<li>
						<IconBox title="Hotline Bộ phận Tuyển sinh - Dịch vụ" content="(028).353.66566">
							<PhoneCall size={20} />
						</IconBox>
					</li>
					<li>
						<IconBox title="Hotline Bộ phận Nhân sự - Tuyển dụng" content="(028).223.66566">
							<PhoneCall size={20} />
						</IconBox>
					</li>
					<li>
						<IconBox title="Email" content="<EMAIL>">
							<Mail size={20} />
						</IconBox>
					</li>
				</ul>
			</div>
		</div>
		<div data-name="last-row" class="text-[0.825rem] text-gray-400 [&>p+p]:mt-1">
			<p
				data-name="copyright"
				set:html={processText(`Copyright © 2025 StudyCare-Education-Group.`).html()}
			/>
			<p
				set:html={processText(
					`Giấy-phép kinh-doanh giáo-dục do Sở Kế-hoạch và Đầu-tư cấp với MST -- 0313301968<br /> Giấy-phép hoạt-động Trung-tâm Anh-ngữ Quốc-tế do Sở Giáo-dục và Đào-tạo cấp theo Quyết-định số 501/QĐ-SGDĐT`
				).html()}
			/>
			<p>
				This site is protected by reCAPTCHA and the Google
				<a class="text-sky-300 hover:underline" href="https://policies.google.com/privacy">
					Privacy Policy
				</a>
				and
				<a class="text-sky-300 hover:underline" href="https://policies.google.com/terms">
					Terms of Service
				</a>
				apply.
			</p>
		</div>
	</div>
</footer>

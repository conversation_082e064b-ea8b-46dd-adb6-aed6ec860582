<template>
	<Carousel
		class="mx-auto w-4/5"
		:opts="{
			align: 'center',
			loop: true
		}"
	>
		<CarouselContent class="items-stretch">
			<CarouselItem v-for="(blogPost, key) in blogPosts" :key class="lg:basis-1/2 xl:basis-1/3">
				<BlogItem v-bind="blogPost" />
			</CarouselItem>
		</CarouselContent>
		<CarouselPrevious />
		<CarouselNext />
	</Carousel>
</template>

<script setup lang="ts">
import {
	Carousel,
	CarouselContent,
	CarouselItem,
	CarouselNext,
	CarouselPrevious
} from "@/components/ui/carousel";
import { BlogItem, blogPosts } from ".";
</script>

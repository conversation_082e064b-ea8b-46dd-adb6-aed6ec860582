import type { APIRoute } from "astro";
import fieldsData from "./_fields.json";
import { StatusCodes, restResponseSuccess, restResponseError } from "@/lib/restResponse";

export const prerender = false;

type RequestData = Record<string, string>;

const fieldsMap = fieldsData as Record<string, Record<string, string>>;

export const POST: APIRoute = async ({ request, url }) => {
	if (request.headers.get("Content-Type") !== "application/json") {
		return restResponseError(StatusCodes.BadRequest, "Content-Type must be application/json");
	}

	const formId = url.searchParams.get("form_id");
	if (!formId) {
		return restResponseError(StatusCodes.BadRequest, "Bad Request");
	}
	if (!fieldsMap[formId]) {
		return restResponseError(StatusCodes.BadRequest, "Bad Request");
	}

	const ggformBaseUrl = `https://docs.google.com/forms/d/e/${formId}/formResponse?usp=pp_url`;

	const submitUrl = new URL(ggformBaseUrl);
	const submitUrlParams = submitUrl.searchParams;

	const requestData: RequestData = await request.json();
	for (const [key, param] of Object.entries(fieldsMap[formId])) {
		submitUrlParams.set(param, requestData[key]);
	}

	const submitUrlString = submitUrl.toString();
	await fetch(submitUrlString);

	return restResponseSuccess(StatusCodes.Ok, submitUrlString);
};

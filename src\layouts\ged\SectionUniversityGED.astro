---
import { VSlider, VSliderItem } from "@/components/astro/VSlider";
import { gedAcceptingUniversities } from "@/components/astro/ged-accepting-universities";
import { Image } from "@/components/astro/assets";
import { TextStroke } from "@/components/astro";

const sliderItemWidth = 304;
const sliderItemHeight = 200;
const sliderDuration = 30; // seconds
---

<section data-name="university-ged" class="py-16">
	<div
		class="container flex flex-col items-stretch justify-center gap-16 lg:flex-row lg:justify-between lg:gap-24"
		data-name="section-content"
	>
		<h2
			class="flex w-full flex-col items-center gap-6 lg:w-[45%] lg:justify-center"
			data-name="section-left"
		>
			<Image src="/site/ged-logo.svg" alt="GED" width={130} height={74} />
			<span class="sr-only">GED</span>
			<TextStroke
				as="span"
				text=" đ<PERSON><PERSON><PERSON> chấp-nhận rộng-rãi bởi các trường <PERSON>ại-học danh-tiếng trên Thế-giới"
				class="text-center text-[1.15rem]/normal font-bold uppercase sm:text-[1.4rem]/normal lg:text-4xl/snug"
				color="white"
				strokeColor="hsl(var(--secondary))"
				strokeWidth={3}
			/>
		</h2>
		<div class="flex w-full justify-center gap-6 lg:w-[55%]" data-name="section-right">
			<div class="hidden h-[36rem] min-[600px]:block">
				<VSlider
					width={sliderItemWidth}
					height={sliderItemHeight}
					duration={sliderDuration}
					quantity={gedAcceptingUniversities.length}
				>
					{
						gedAcceptingUniversities.map((item, index) => (
							<VSliderItem position={index}>
								<Image
									src={item.src}
									alt={item.alt}
									width={sliderItemWidth}
									height={sliderItemHeight}
								/>
							</VSliderItem>
						))
					}
				</VSlider>
			</div>
			<div class="h-[36rem]">
				<VSlider
					width={sliderItemWidth}
					height={sliderItemHeight}
					duration={sliderDuration}
					quantity={gedAcceptingUniversities.length}
					reverse={true}
				>
					{
						gedAcceptingUniversities.map((item, index) => (
							<VSliderItem position={index}>
								<Image
									src={item.src}
									alt={item.alt}
									width={sliderItemWidth}
									height={sliderItemHeight}
								/>
							</VSliderItem>
						))
					}
				</VSlider>
			</div>
		</div>
	</div>
</section>

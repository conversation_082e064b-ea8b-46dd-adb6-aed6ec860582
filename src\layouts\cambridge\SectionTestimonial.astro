---
import { Picture, PictureBackground } from "@/components/astro/assets";
import { TextStroke } from "@/components/astro";
import { But<PERSON> } from "@/components/ui/button";

import testimonialsBgImg from "@/assets/testimonials-bg.png";
import { TestimonialsCarousel, testimonials } from "@/components/custom/TestimonialsCarousel";
import { PopupContactForm } from "@/components/custom/PopupContactForm";

const testimonialsImgFormats = ["avif", "webp"];
const testimonialsImgWidths = [320, 330, 525, 640, 660, 960, 990, 1050, 1575];
const testimonialsImgSizes = "(max-width: 425px) 330px, (max-width: 768px) 525px, 320px";
---

<PictureBackground
	src={testimonialsBgImg}
	alt="Ảnh nền mây trời"
	formats={["avif", "webp"]}
	tagAttributes={{ "data-name": "testimonials", class: "py-16" }}
>
	<div class="container">
		<div class="mb-6 flex justify-center" data-name="section-title">
			<h2 class="mb-4 flex flex-col justify-center gap-2 font-bold">
				<span class="flex justify-center gap-2">
					<TextStroke
						as="span"
						class="inline-block text-3xl md:text-4xl"
						text="StudyCare "
						color="hsl(var(--secondary))"
						strokeColor="white"
					/>
					<TextStroke
						as="span"
						class="inline-block text-3xl md:text-4xl"
						text="Education "
						color="hsl(var(--primary))"
						strokeColor="white"
					/>
				</span>
				<TextStroke
					as="span"
					text="Tự hào đồng hành cùng tất cả học sinh"
					class="inline-block text-center text-2xl/snug uppercase md:text-4xl/snug"
					color="white"
					strokeColor="hsl(var(--primary))"
					strokeWidth={2}
				/>
			</h2>
		</div>
		<div data-name="section-content">
			<TestimonialsCarousel client:load>
				<Picture
					slot={testimonials[0].slot}
					src={testimonials[0].img}
					alt={testimonials[0].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[1].slot}
					src={testimonials[1].img}
					alt={testimonials[1].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[2].slot}
					src={testimonials[2].img}
					alt={testimonials[2].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[3].slot}
					src={testimonials[3].img}
					alt={testimonials[3].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[4].slot}
					src={testimonials[4].img}
					alt={testimonials[4].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[5].slot}
					src={testimonials[5].img}
					alt={testimonials[5].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[6].slot}
					src={testimonials[6].img}
					alt={testimonials[6].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[7].slot}
					src={testimonials[7].img}
					alt={testimonials[7].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[8].slot}
					src={testimonials[8].img}
					alt={testimonials[8].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[9].slot}
					src={testimonials[9].img}
					alt={testimonials[9].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[10].slot}
					src={testimonials[10].img}
					alt={testimonials[10].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[11].slot}
					src={testimonials[11].img}
					alt={testimonials[11].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[12].slot}
					src={testimonials[12].img}
					alt={testimonials[12].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[13].slot}
					src={testimonials[13].img}
					alt={testimonials[13].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[14].slot}
					src={testimonials[14].img}
					alt={testimonials[14].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[15].slot}
					src={testimonials[15].img}
					alt={testimonials[15].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[16].slot}
					src={testimonials[16].img}
					alt={testimonials[16].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[17].slot}
					src={testimonials[17].img}
					alt={testimonials[17].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[18].slot}
					src={testimonials[18].img}
					alt={testimonials[18].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[19].slot}
					src={testimonials[19].img}
					alt={testimonials[19].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[20].slot}
					src={testimonials[20].img}
					alt={testimonials[20].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[21].slot}
					src={testimonials[21].img}
					alt={testimonials[21].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[22].slot}
					src={testimonials[22].img}
					alt={testimonials[22].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[23].slot}
					src={testimonials[23].img}
					alt={testimonials[23].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[24].slot}
					src={testimonials[24].img}
					alt={testimonials[24].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[25].slot}
					src={testimonials[25].img}
					alt={testimonials[25].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[26].slot}
					src={testimonials[26].img}
					alt={testimonials[26].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[27].slot}
					src={testimonials[27].img}
					alt={testimonials[27].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
				<Picture
					slot={testimonials[28].slot}
					src={testimonials[28].img}
					alt={testimonials[28].name}
					formats={testimonialsImgFormats}
					widths={testimonialsImgWidths}
					sizes={testimonialsImgSizes}
				/>
			</TestimonialsCarousel>
			<div class="mt-8 flex justify-center">
				<PopupContactForm
					formID="1FAIpQLSfkHjhmuCB4C-UGv9as6VPGj0nzuyJR72N90PoqeL4TH8HZMQ"
					id="testimonials-contact-form"
					client:load
				>
					<Button as="button" id="testimonials-contact-form-popup">
						Đăng ký học thử miễn phí
					</Button>
				</PopupContactForm>
			</div>
		</div>
	</div>
</PictureBackground>

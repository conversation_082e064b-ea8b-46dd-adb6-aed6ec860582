---
interface Props {
	size?: number;
}

const { size = 24 } = Astro.props;
---

<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width={size} height={size} fill="currentColor"><path d="M23 17.26a6.53 6.53 0 0 1-.64 3.26 4.71 4.71 0 0 1-3.08 2.3 10.2 10.2 0 0 1-2.21.18H8.22a17.35 17.35 0 0 1-3.45-.16 4.58 4.58 0 0 1-3.29-2.58A6.25 6.25 0 0 1 1 17.79V6.2a5.23 5.23 0 0 1 1.29-3.6 4.66 4.66 0 0 1 2.8-1.49A12.44 12.44 0 0 1 7.37 1l-.06.05a7.8 7.8 0 0 0-2.42 2.6 12.7 12.7 0 0 0-1.53 6.85A12.17 12.17 0 0 0 4.9 16c.16.3.43.55.44.92A3 3 0 0 1 4.46 19a.36.36 0 0 0 .11.1 5.61 5.61 0 0 0 2.79-.49 12.62 12.62 0 0 0 5.77 1.81 15.06 15.06 0 0 0 6-.71 9.06 9.06 0 0 0 3.8-2.4Z"/><path d="M15.54 8.21h.92v5.36c-.3 0-.8.15-.91-.25-.02-1.7 0-3.41-.01-5.11ZM6.2 8.27h4.26a1.28 1.28 0 0 1-.21.81c-1 1.2-1.92 2.43-2.89 3.63h3.09c0 .24.07.52-.09.73s-.27.12-.42.12H6.07a1.18 1.18 0 0 1 .2-.8c1-1.2 1.93-2.39 2.88-3.6H6.21c-.01-.29-.01-.59-.01-.89ZM18.73 9.45a2.1 2.1 0 0 1 1.06 4.06 2.09 2.09 0 0 1-2.44-.92 2.13 2.13 0 0 1-.06-2.05 2.16 2.16 0 0 1 1.44-1.09Zm0 .91a1.24 1.24 0 0 0 .75 2.35 1.23 1.23 0 1 0-.75-2.35ZM11.16 10.2a2 2 0 0 1 2.11-.69 2.61 2.61 0 0 1 .7.32v-.27h.86v4a3.29 3.29 0 0 1-.63 0c-.15-.06-.19-.22-.25-.35a2 2 0 0 1-2.69-.27 2.09 2.09 0 0 1-.1-2.74Zm1.16.18a1.22 1.22 0 1 0 1.56.68 1.21 1.21 0 0 0-1.56-.68Z"/></svg>
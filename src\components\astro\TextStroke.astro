---
import type { HTMLTag, HTMLAttributes, Polymorphic } from "astro/types";
import { cn } from "@/lib/utils";
import { processText } from "@/lib/features";

type Props<Tag extends HTMLTag> = Polymorphic<{
	as: Tag;
	text: string;
	class?: HTMLAttributes<"h2">["class"];
	color?: string;
	strokeColor?: string;
	strokeWidth?: number;
}>;

const {
	as = "h2",
	text,
	color = "#fff",
	strokeColor = "hsl(var(--primary))",
	strokeWidth = 1,
	class: propsClass,
	...props
} = Astro.props;

const generateTextShadow = (strokeWidth: number) => {
	const numberOfShadows = Math.ceil(2 * Math.PI * strokeWidth);

	let shadow = "";
	for (let i = 0; i < numberOfShadows; i++) {
		const theta = (2 * Math.PI * i) / numberOfShadows;
		const offsetX = strokeWidth * Math.cos(theta);
		const offsetY = strokeWidth * Math.sin(theta);
		const trailingComma = i === numberOfShadows - 1 ? "" : ",";

		shadow += `${offsetX}px ${offsetY}px 0 var(--ts-stroke-color)${trailingComma}`;
	}

	return shadow;
};

const stroke = generateTextShadow(strokeWidth);
const textOffset = `-${strokeWidth * 0.8}px`;

const Element = as;

const htmlText = processText(text).html();
const dataText = processText(text).unicode();
---

<Element
	data-text-stroke={dataText}
	class={cn("relative", propsClass)}
	style={`--ts-color: ${color}; --ts-stroke-color: ${strokeColor}; --ts-text-offset: ${textOffset}; --ts-stroke: ${stroke};`}
	{...props}
	set:html={htmlText}
/>

<style is:global>
	[data-text-stroke] {
		color: var(--ts-stroke-color);
		text-shadow: var(--ts-stroke);
	}

	[data-text-stroke]::after {
		content: attr(data-text-stroke);
		font: inherit;
		color: var(--ts-color);
		text-shadow: var(--ts-stroke);

		position: absolute;
		left: 0;
		top: 0;
		-webkit-transform: translate3d(0, var(--ts-text-offset), 0);
		-moz-transform: translate3d(0, var(--ts-text-offset), 0);
		-ms-transform: translate3d(0, var(--ts-text-offset), 0);
		transform: translate3d(0, var(--ts-text-offset), 0);
		z-index: 100;
	}
</style>

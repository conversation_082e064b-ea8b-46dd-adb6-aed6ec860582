<template>
	<Tabs default-value="nct" class="flex flex-col gap-4 lg:flex-col-reverse">
		<TabsList class="bg-transparent">
			<Carousel class="relative w-full" :opts="{ align: 'center' }">
				<CarouselContent class="-ml-0 flex gap-4">
					<CarouselItem
						class="basis-1/4 pl-0 md:basis-1/5 xl:basis-1/6"
						v-for="(teacher, key) in teachers"
						:key
					>
						<TabsTrigger
							class="w-26 h-26 overflow-hidden rounded-2xl border-4 border-gray-500 p-0 opacity-50 data-[state=active]:border-primary data-[state=active]:opacity-100"
							:value="teacher.slot"
						>
							<slot :name="teacher.slot"></slot>
						</TabsTrigger>
					</CarouselItem>
				</CarouselContent>
			</Carousel>
		</TabsList>
		<div class="rounded-2xl border border-b-4 border-primary bg-white p-6 shadow-xl">
			<TabsContent
				v-for="(teacher, key) in teachers"
				:value="teacher.slot"
				:key
				class="mt-0 flex flex-col gap-6 xl:flex-row"
			>
				<div
					data-name="img"
					class="h-32 w-32 shrink-0 overflow-hidden rounded-xl border-4 border-primary"
				>
					<slot :name="teacher.slot"></slot>
				</div>
				<div data-name="content" class="text-sm sm:text-base md:text-lg">
					<h3 data-name="fullName" class="text-lg font-bold text-primary md:text-xl">
						{{ teacher.label }}
					</h3>
					<span
						v-if="teacher.title"
						data-name="title"
						v-html="teacher.title"
						class="inline-block italic text-primary"
					></span>
					<ul
						data-name="academic-titles"
						class="mt-2 flex flex-col gap-1 italic [&>li>strong]:not-italic"
					>
						<li
							v-for="(academicTitle, k) in teacher.academicTitles"
							:key="k"
							v-html="academicTitle"
						></li>
					</ul>
				</div>
			</TabsContent>
		</div>
	</Tabs>
</template>

<script setup lang="ts">
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Carousel, CarouselContent, CarouselItem } from "@/components/ui/carousel";
import { teachers } from ".";
</script>

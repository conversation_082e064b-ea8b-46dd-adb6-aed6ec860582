<template>
	<Carousel
		class="mx-auto w-4/5 rounded-2xl border-2 border-b-4 border-secondary bg-white p-1 shadow-xl md:p-2 lg:p-4 xl:w-full"
		:opts="{
			align: 'center',
			loop: true
		}"
	>
		<CarouselContent class="items-stretch">
			<CarouselItem v-for="(tiktok, key) in props.videos" :key class="lg:basis-1/2">
				<template v-html="tiktok" />
			</CarouselItem>
		</CarouselContent>
		<CarouselPrevious />
		<CarouselNext />
	</Carousel>
</template>

<script setup lang="ts">
import {
	Carousel,
	CarouselContent,
	CarouselItem,
	CarouselNext,
	CarouselPrevious
} from "@/components/ui/carousel";

interface Props {
	videos: string[];
}

const props = defineProps<Props>();
</script>

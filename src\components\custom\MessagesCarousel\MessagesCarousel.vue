<template>
	<Carousel
		class="mx-auto w-4/5"
		:opts="{
			align: 'center',
			loop: true
		}"
	>
		<CarouselContent class="items-center">
			<CarouselItem v-for="(message, key) in messages" :key class="lg:basis-1/2">
				<div class="overflow-hidden rounded-xl">
					<slot :name="message.slot"></slot>
				</div>
			</CarouselItem>
		</CarouselContent>
		<CarouselPrevious />
		<CarouselNext />
	</Carousel>
</template>

<script setup lang="ts">
import {
	Carousel,
	CarouselContent,
	CarouselItem,
	CarouselNext,
	CarouselPrevious
} from "@/components/ui/carousel";
import { messages } from ".";
</script>

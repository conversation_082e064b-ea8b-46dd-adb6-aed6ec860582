<template>
	<article
		:class="
			cn(
				'h-full rounded-2xl border border-b-4 border-primary bg-white p-2 sm:p-4 md:p-6',
				props.class
			)
		"
	>
		<a
			:href
			:title="imgTitle"
			class="group aspect-h-[630] aspect-w-[1200] mb-2 block overflow-hidden rounded-lg"
		>
			<img
				:src="featureImg"
				:alt="imgTitle"
				loading="lazy"
				decoding="async"
				width="600"
				height="315"
				class="h-full w-full object-cover transition-transform group-hover:scale-105 group-hover:opacity-90"
			/>
		</a>
		<a
			:href
			class="mb-1 block text-lg font-bold transition-all hover:text-primary hover:underline md:text-xl"
		>
			<component :is="titleTag">{{ title }}</component>
		</a>
		<p class="mb-2 text-gray-700 md:text-lg">{{ description }}</p>
		<a :href class="flex gap-2 text-primary transition-all hover:text-sky-800 hover:underline">
			<span>Đ<PERSON><PERSON> b<PERSON><PERSON> viết đ<PERSON> đ<PERSON></span>
			<ExternalLink :size="16" class="mt-0.5" />
		</a>
	</article>
</template>

<script setup lang="ts">
import { ExternalLink } from "lucide-vue-next";
import { cn } from "@/lib/utils";

import type { HTMLAttributes } from "vue";
import type { BlogPost } from ".";

interface Props extends BlogPost {
	class?: HTMLAttributes["class"];
}

const { titleTag = "h3", featureImg, href, title, description, ...props } = defineProps<Props>();

const imgTitle = `Đọc bài viết ${title}`;
</script>

---
import { Picture, PictureBackground, Image } from "@/components/astro/assets";
import { TextStroke } from "@/components/astro";
import { ContactForm } from "@/components/custom/ContactForm";
import { Button } from "@/components/ui/button";
import { <PERSON>alo } from "@/components/icons";
import { processText } from "@/lib/features";

import ctaBgImg from "@/assets/cta-bg.jpg";
import studentsImg from "@/assets/students.png";
---

<PictureBackground
	src={ctaBgImg}
	alt="Ảnh nền bầu trời bình minh"
	tagAttributes={{ "data-name": "cta", class: "py-16 bg-[#014353]" }}
>
	<div class="container flex flex-col items-center justify-center gap-6">
		<h2
			class="flex flex-col items-center gap-1 text-center text-[1.75rem]/snug font-bold uppercase md:text-4xl/snug"
		>
			<span class="inline-flex flex-col items-center md:flex-row md:gap-2">
				<TextStroke as="span" text="Quý phụ-huynh " strokeWidth={3} />
				<TextStroke as="span" text="còn đắn-đo gì nữa? " strokeWidth={3} />
			</span>
			<TextStroke
				as="span"
				text=" Hãy liên-hệ ngay "
				strokeWidth={3}
				class="animate-custom-color inline-block"
			/>
			<TextStroke
				as="span"
				text="để được tư-vấn miễn-phí"
				class="inline-block sm:max-w-72 md:max-w-full"
				strokeWidth={3}
			/>
		</h2>
		<div
			data-name="contact-form-container"
			class="w-full max-w-xl rounded-2xl border-2 border-b-4 border-primary bg-white p-6 shadow-xl"
		>
			<h3
				class="mb-6 inline-block px-2 text-center text-lg font-bold text-primary sm:px-4 sm:text-xl md:px-16 md:text-3xl lg:px-0 lg:text-2xl"
				set:html={processText("Đăng-ký lớp-học trải-nghiệm và tư-vấn miễn-phí").html()}
			/>
			<Picture
				src={studentsImg}
				alt="Ảnh minh họa các học sinh"
				widths={[320, 640, 1280]}
				sizes="320px"
				class="mx-auto mb-6 w-full max-w-xs motion-safe:animate-wiggle"
			/>
			<ContactForm
				formID="1FAIpQLSfHh5YxhivW2CE2jRGi2GbNewxxb1TPigzTaXt-hQHFR9SjEA"
				id="cta-contact-form"
				client:idle
			/>
		</div>
		<div
			data-name="contact-form-container"
			class="flex w-full max-w-xl flex-col items-center gap-4 rounded-2xl border-2 border-b-4 border-primary bg-white p-6 shadow-xl"
		>
			<h3
				class="text-center text-lg font-bold text-gray-600 sm:text-xl lg:text-2xl"
				set:html={processText(
					`Hoặc liên-hệ trực-tiếp với <span class="text--primary">StudyCare</span>-<span class="text--secondary">Education</span> qua-Zalo`
				).html()}
			/>
			<Button
				as="a"
				variant="primary"
				href="https://zalo.me/0983531175"
				class="gap-2"
				id="cta-contact-btn"
			>
				<Zalo size={24} />Liên hệ tư vấn
			</Button>
		</div>
	</div>
</PictureBackground>

<style is:global>
	@media (prefers-reduced-motion: no-preference) {
		.animate-custom-color {
			animation: customColor 2s ease-in-out infinite;
		}
		@keyframes customColor {
			0%,
			100% {
				--ts-color: #fff;
				--ts-stroke-color: hsl(var(--primary));
			}
			25% {
				--ts-color: hsl(var(--primary));
				--ts-stroke-color: #fff;
			}
			50% {
				--ts-color: hsl(var(--primary-foreground));
				--ts-stroke-color: hsl(var(--primary));
			}
			75% {
				--ts-color: hsl(var(--primary));
				--ts-stroke-color: hsl(var(--primary-foreground));
			}
		}
	}
</style>

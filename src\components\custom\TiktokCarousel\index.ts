export { default as TiktokCarousel } from "./TiktokCarousel.vue";

const tiktokVideoURLs = [
	"https://www.tiktok.com/@studycare_education/video/7478220825867439381",
	"https://www.tiktok.com/@studycare_education/video/7093418940608597249",
	"https://www.tiktok.com/@studycare_education/video/7302794483765185800"
];

const tiktokOembedAPIEndpoint = "https://www.tiktok.com/oembed";
const tiktokOembedAPIParam = "url";

const fetchTiktokVideoEmbedScript = async (tiktokVideoURL: string): Promise<string> => {
	const tiktokOembedAPI = new URL(tiktokOembedAPIEndpoint);
	tiktokOembedAPI.searchParams.set(tiktokOembedAPIParam, tiktokVideoURL);

	const response = await fetch(tiktokOembedAPI);
	const data = await response.json();

	return data.html.replace('<script async src="https://www.tiktok.com/embed.js"></script>', "");
};

const fetchTiktokVideoEmbedScripts = (tiktokVideoURLs: string[]): Promise<string>[] => {
	return tiktokVideoURLs.map(fetchTiktokVideoEmbedScript);
};

export const getTiktokVideos = async (): Promise<string[]> => {
	return await Promise.all(fetchTiktokVideoEmbedScripts(tiktokVideoURLs));
};

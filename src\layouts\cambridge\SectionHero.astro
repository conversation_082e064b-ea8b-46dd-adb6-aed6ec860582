---
import { Picture, PictureBackground, getImage } from "@/components/astro/assets";
import { TextStroke, type Component } from "@/components/astro";
import { Button } from "@/components/ui/button";
import { ContactForm } from "@/components/custom/ContactForm";

import studentsImg from "@/assets/students.png";
import heroBackgroundImg from "@/assets/hero-background.webp";
import cloudImg from "@/assets/cloud.png";
import rocketImg from "@/assets/rocket.png";
import { Zalo } from "@/components/icons";

const mainContentList = [
	"L<PERSON> trình học tập <strong>chuẩn quốc tế</strong> các môn học: <em> To<PERSON> - <PERSON><PERSON><PERSON> họ<PERSON> - <PERSON>a <PERSON> - <PERSON>t <PERSON> - <PERSON>ọ<PERSON> - Tiếng <PERSON>h</em>",
	"Tạm biệt nỗi lo các <strong>môn khó nhằn</strong>: <em>ICT - ESL - SAT - Global Perspectives - Economics - Business Studies - Literature in English</em>",
	"<strong><PERSON><PERSON><PERSON><PERSON> thi đầu vào <PERSON></strong> c<PERSON>c môn <em><PERSON><PERSON> - <PERSON>ă<PERSON> - <PERSON><PERSON><PERSON>ng <PERSON> - <PERSON><PERSON> duy logic</em>, <strong>phù hợp mọi trình độ</strong>",
	"Trải nghiệm <strong>học thử MIỄN PHÍ môn Science</strong> chương trình Cambridge vào Thứ 7 hàng tuần",
	"Học phí rất hợp lý chỉ từ <strong>1.200.000đ mỗi tháng</strong>. Học tháng nào đóng tháng đó",
	{
		component: TextStroke<"span">,
		props: {
			as: "span",
			text: "Cam kết hoàn phí 100% nếu không tiến bộ sau 1 tháng",
			strokeColor: "hsl(var(--secondary))"
		}
	}
] satisfies (string | Component<typeof TextStroke<"span">>)[];

const cloudBgImg = await getImage({ src: cloudImg, format: "webp" });
---

<PictureBackground
	src={heroBackgroundImg}
	alt="Ảnh nền mây trời"
	loading="eager"
	fetchpriority="high"
	tagAttributes={{
		"data-name": "hero",
		class: "bg-sky-100 py-16",
		style: `--cloud-bg-img: url(${cloudBgImg.src});`
	}}
>
	<div class="container flex flex-col gap-16 lg:flex-row lg:gap-8" data-name="section-content">
		<div data-name="section-left" class="lg:w-3/5 xl:w-2/3">
			<div data-name="hero-title" class="mb-6 flex flex-col items-center gap-2">
				<TextStroke
					as="h1"
					text="Chấp cánh ước mơ"
					class="text-center text-5xl/tight font-extrabold uppercase xl:text-6xl/tight"
					strokeColor="#1165a2"
					strokeWidth={4}
				/>
				<TextStroke
					as="p"
					text="Cho bao thế hệ học viên"
					class="max-w-2xl text-center text-xl font-bold uppercase sm:text-2xl md:text-3xl"
					color="hsl(var(--primary-foreground))"
					strokeColor="hsl(var(--primary))"
					strokeWidth={2}
				/>
				<TextStroke
					as="p"
					text="Chương trình Cambridge"
					class="max-w-2xl text-center text-xl font-bold uppercase sm:text-2xl md:text-3xl"
					color="hsl(var(--primary-foreground))"
					strokeColor="hsl(var(--primary))"
					strokeWidth={2}
				/>
				<p class="-order-1 mb-4 flex flex-wrap justify-center gap-2 text-2xl font-bold md:text-3xl">
					<TextStroke
						as="span"
						class="inline-block"
						text="StudyCare"
						color="hsl(var(--secondary))"
						strokeColor="white"
						strokeWidth={2}
					/>
					<TextStroke
						as="span"
						class="inline-block"
						text="Education"
						color="hsl(var(--primary))"
						strokeColor="white"
						strokeWidth={2}
					/>
				</p>
			</div>
			<div
				data-name="hero-content"
				class="mx-auto mb-4 w-full -rotate-1 bg-white p-3 shadow-md md:max-w-xl lg:max-w-[45rem]"
			>
				<ul
					data-name="hero-list"
					class="flex flex-col gap-2 p-6 text-base transition-all md:p-8 lg:text-lg"
				>
					{
						mainContentList.map((content) => (
							<li>
								<Picture
									src={rocketImg}
									alt="Icon tên lửa"
									width={32}
									height={32}
									widths={[32, 64, 128]}
									sizes="32px"
									loading="eager"
									fetchpriority="high"
								/>
								{typeof content === "string" ? (
									<span set:html={content} />
								) : (
									<content.component {...content.props} />
								)}
							</li>
						))
					}
				</ul>
			</div>
			<div data-name="hero-buttons" class="flex justify-center">
				<Button
					as="a"
					variant="primary"
					href="https://zalo.me/0983531175"
					class="gap-2"
					id="hero-contact-btn"
				>
					<Zalo size={24} />Liên hệ tư vấn
				</Button>
			</div>
		</div>
		<div data-name="section-right" class="grid place-items-center lg:w-2/5 xl:w-1/3">
			<div
				data-name="contact-form-container"
				class="w-full max-w-xl rounded-2xl border-2 border-b-4 border-primary bg-white p-6 shadow-xl"
			>
				<h2
					class="mb-6 inline-block px-2 text-center text-lg font-bold text-primary sm:px-4 sm:text-xl md:px-16 md:text-3xl lg:px-0 lg:text-2xl xl:px-6"
				>
					Đăng ký lớp học trải nghiệm và tư vấn miễn phí
				</h2>
				<Picture
					src={studentsImg}
					alt="Ảnh minh họa các học sinh"
					loading="eager"
					formats={["avif", "webp"]}
					fetchpriority="high"
					widths={[320, 640, 1280]}
					sizes="320px"
					class="mx-auto mb-6 w-full max-w-xs motion-safe:animate-wiggle"
				/>
				<ContactForm
					formID="1FAIpQLSfkHjhmuCB4C-UGv9as6VPGj0nzuyJR72N90PoqeL4TH8HZMQ"
					id="hero-contact-form"
					client:load
				/>
			</div>
		</div>
	</div>
</PictureBackground>

<style is:global>
	[data-name="hero"]::before,
	[data-name="hero"]::after {
		content: "";
		width: 100%;
		height: 100%;
		display: block;
		background: var(--cloud-bg-img) 0 0 / contain repeat-y;
		top: 0;
		left: 0;
		position: absolute;
		z-index: 1;
	}
	[data-name="hero"]::before {
		animation:
			cloudFly 30s 15s infinite backwards linear,
			flicker 10s infinite backwards linear;
	}
	[data-name="hero"]::after {
		animation:
			cloudFly 30s infinite backwards linear,
			flicker 10s infinite backwards linear;
	}

	@keyframes cloudFly {
		0% {
			transform: translateX(-50%);
		}
		100% {
			transform: translateX(100%);
		}
	}
	@keyframes flicker {
		0%,
		100% {
			opacity: 1;
		}
		50% {
			opacity: 0.3;
		}
	}

	[data-name="hero-content"] {
		border-top-left-radius: 20px 50px;
		border-top-right-radius: 25px 45px;
		border-bottom-left-radius: 40px 10px;
		border-bottom-right-radius: 30px 55px;
	}
	[data-name="hero-content"]:hover {
		--tw-rotate: 1deg;
	}
	[data-name="hero-list"] {
		background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='16' ry='16' stroke='%231374b9' stroke-width='2' stroke-dasharray='2%2c4' stroke-dashoffset='3' stroke-linecap='square'/%3e%3c/svg%3e");
		border-radius: 16px;
	}
	[data-name="hero-list"] li {
		@apply flex items-center gap-4;
	}
	[data-name="hero-list"] picture {
		@apply h-8 w-8 shrink-0;
	}
	[data-name="hero-list"] strong {
		@apply text-primary;
	}
</style>

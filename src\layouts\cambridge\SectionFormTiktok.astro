---
import { Picture } from "astro:assets";
import { TextStroke } from "@/components/astro";
import { ContactForm } from "@/components/custom/ContactForm";
import { TiktokCarousel, getTiktokVideos } from "@/components/custom/TiktokCarousel";
import { processText } from "@/lib/features";

import studentsImg from "@/assets/students.png";

const tiktokVideos = await getTiktokVideos();
---

<section data-name="form-tiktok" class="bg-sky-50 bg-cover bg-center bg-no-repeat py-16">
	<div
		class="container flex flex-col items-center justify-center gap-16 lg:gap-24 xl:flex-row xl:justify-between"
		data-name="section-content"
	>
		<div data-name="section-left" class="grid w-full place-items-center xl:w-1/3">
			<div class="flex justify-center">
				<TextStroke
					as="h2"
					text="Hã<PERSON> nhanh-tay đăng-ký ngay!"
					class="mb-6 text-center text-4xl/tight font-bold uppercase"
					color="white"
					strokeColor="hsl(var(--primary))"
					strokeWidth={3}
				/>
			</div>
			<div
				data-name="contact-form-container"
				class="w-full max-w-xl rounded-2xl border-2 border-b-4 border-primary bg-white p-6 shadow-xl"
			>
				<h3
					class="mb-6 inline-block px-2 text-center text-lg font-bold text-primary sm:px-4 sm:text-xl md:px-16 md:text-3xl lg:px-0 lg:text-2xl xl:px-6"
					set:html={processText("Đăng-ký lớp-học trải-nghiệm và tư-vấn miễn-phí").html()}
				/>
				<Picture
					src={studentsImg}
					alt="Ảnh minh họa các học sinh"
					widths={[320, 640, 1280]}
					sizes="320px"
					class="mx-auto mb-6 w-full max-w-xs motion-safe:animate-wiggle"
				/>
				<ContactForm
					formID="1FAIpQLSfkHjhmuCB4C-UGv9as6VPGj0nzuyJR72N90PoqeL4TH8HZMQ"
					id="second-contact-form"
					client:idle
				/>
			</div>
		</div>
		<div data-name="section-right" class="w-full xl:w-2/3">
			<div class="flex justify-center">
				<TextStroke
					as="h2"
					text="Chia-sẻ từ các bạn học-viên khóa-trước"
					class="mb-6 text-center text-4xl/tight font-bold uppercase md:max-w-md lg:max-w-full"
					color="white"
					strokeColor="hsl(var(--secondary))"
					strokeWidth={3}
				/>
			</div>
			<TiktokCarousel videos={tiktokVideos} client:load />
		</div>
	</div>
</section>

---
import { cn } from "@/lib/utils"; // Corrected path

interface Props {
	quantity: number;
	as?: string;
	width?: number;
	height?: number;
	duration?: number;
	reverse?: boolean;
}

const {
	quantity,
	as = "div",
	width = 100,
	height = 50,
	duration = 10,
	reverse = false
} = Astro.props;

const Element = as;
---

<Element
	data-name="v-slider"
	data-v-slider-reverse={reverse}
	class={cn("h-full overflow-hidden", Astro.props.class)}
	style={`--width: ${width}px; --height: ${height}px; --quantity: ${quantity}; --duration: ${duration}s`}
>
	<ul data-name="v-list" class="relative flex h-full flex-col">
		<slot />
	</ul>
</Element>

<style is:inline>
	[data-name="v-slider"] {
		width: var(--width); /* Changed from height */
		mask-image: linear-gradient(
			to bottom,
			transparent,
			#000 10% 90%,
			transparent
		); /* Changed direction */
	}
	[data-name="v-slider"] [data-name="v-list"] {
		min-height: calc(var(--height) * var(--quantity)); /* Changed from min-width */
	}
	[data-name="v-slider"] [data-name="v-list"] [data-name="v-item"] {
		height: var(--height); /* Changed from width */
		width: var(--width); /* Changed from height */
		animation: vSlideDown var(--duration) linear infinite;
		transition: filter 0.5s;
		animation-delay: calc(
			((var(--duration) / var(--quantity)) * (var(--position) - 1) - var(--duration))
		) !important;
	}

	@keyframes vSlideUp {
		from {
			top: 100%; /* Changed from left */
		}
		to {
			top: calc(var(--height) * -1); /* Changed from left and var(--width) */
		}
	}

	[data-name="v-slider"]:hover [data-name="v-item"] {
		animation-play-state: paused !important;
		filter: grayscale(1);
	}
	[data-name="v-slider"] [data-name="v-item"]:hover {
		filter: grayscale(0);
	}
	[data-name="v-slider"][data-v-slider-reverse] [data-name="v-item"] {
		animation: vSlideUp var(--duration) linear infinite;
	}

	@keyframes vSlideDown {
		from {
			top: calc(var(--height) * -1); /* Changed from left and var(--width) */
		}
		to {
			top: 100%; /* Changed from left */
		}
	}
</style>

---
import { Picture } from "@/components/astro/assets";
import { TextStroke } from "@/components/astro";
import { MessagesCarousel, messages } from "@/components/custom/MessagesCarousel";
import { PopupContactForm } from "@/components/custom/PopupContactForm";
import { But<PERSON> } from "@/components/ui/button";

const messagesImgWidths = (width: number) => [290, 380, 580, width];
const messagesImgSizes =
	"(max-width: 425px) 290px, (max-width: 768px) 580px, (max-width: 1024px) 380px, 580px";
---

<section data-name="messages" class="bg-[#045066] py-16">
	<div class="container">
		<div data-name="section-title" class="mb-8 flex justify-center">
			<TextStroke
				as="h2"
				text="Học sinh nói gì về chất lượng giảng viên?"
				class="text-center text-4xl/tight font-bold uppercase"
				color="white"
				strokeColor="hsl(var(--primary))"
				strokeWidth={3}
			/>
		</div>
		<div data-name="section-content">
			<MessagesCarousel client:load>
				<Picture
					slot={messages[0].slot}
					src={messages[0].img}
					alt={messages[0].alt}
					widths={messagesImgWidths(messages[0].img.width)}
					sizes={messagesImgSizes}
				/>
				<Picture
					slot={messages[1].slot}
					src={messages[1].img}
					alt={messages[1].alt}
					widths={messagesImgWidths(messages[1].img.width)}
					sizes={messagesImgSizes}
				/>
				<Picture
					slot={messages[2].slot}
					src={messages[2].img}
					alt={messages[2].alt}
					widths={messagesImgWidths(messages[2].img.width)}
					sizes={messagesImgSizes}
				/>
				<Picture
					slot={messages[3].slot}
					src={messages[3].img}
					alt={messages[3].alt}
					widths={messagesImgWidths(messages[3].img.width)}
					sizes={messagesImgSizes}
				/>
				<Picture
					slot={messages[4].slot}
					src={messages[4].img}
					alt={messages[4].alt}
					widths={messagesImgWidths(messages[4].img.width)}
					sizes={messagesImgSizes}
				/>
				<Picture
					slot={messages[5].slot}
					src={messages[5].img}
					alt={messages[5].alt}
					widths={messagesImgWidths(messages[5].img.width)}
					sizes={messagesImgSizes}
				/>
				<Picture
					slot={messages[6].slot}
					src={messages[6].img}
					alt={messages[6].alt}
					widths={messagesImgWidths(messages[6].img.width)}
					sizes={messagesImgSizes}
				/>
				<Picture
					slot={messages[7].slot}
					src={messages[7].img}
					alt={messages[7].alt}
					widths={messagesImgWidths(messages[7].img.width)}
					sizes={messagesImgSizes}
				/>
				<Picture
					slot={messages[8].slot}
					src={messages[8].img}
					alt={messages[8].alt}
					widths={messagesImgWidths(messages[8].img.width)}
					sizes={messagesImgSizes}
				/>
				<Picture
					slot={messages[9].slot}
					src={messages[9].img}
					alt={messages[9].alt}
					widths={messagesImgWidths(messages[9].img.width)}
					sizes={messagesImgSizes}
				/>
				<Picture
					slot={messages[10].slot}
					src={messages[10].img}
					alt={messages[10].alt}
					widths={messagesImgWidths(messages[10].img.width)}
					sizes={messagesImgSizes}
				/>
				<Picture
					slot={messages[11].slot}
					src={messages[11].img}
					alt={messages[11].alt}
					widths={messagesImgWidths(messages[11].img.width)}
					sizes={messagesImgSizes}
				/>
				<Picture
					slot={messages[12].slot}
					src={messages[12].img}
					alt={messages[12].alt}
					widths={messagesImgWidths(messages[12].img.width)}
					sizes={messagesImgSizes}
				/>
				<Picture
					slot={messages[13].slot}
					src={messages[13].img}
					alt={messages[13].alt}
					widths={messagesImgWidths(messages[13].img.width)}
					sizes={messagesImgSizes}
				/>
				<Picture
					slot={messages[14].slot}
					src={messages[14].img}
					alt={messages[14].alt}
					widths={messagesImgWidths(messages[14].img.width)}
					sizes={messagesImgSizes}
				/>
			</MessagesCarousel>
			<div class="mt-8 flex justify-center">
				<PopupContactForm
					formID="1FAIpQLSeXo2dqpMX8_3FmlUPitmCmIZTdIsdf_jMO5s1Pi5ZyLRlTIg"
					id="messages-contact-form"
					client:load
				>
					<Button as="button" id="messages-contact-form-popup">Đăng ký học thử miễn phí</Button>
				</PopupContactForm>
			</div>
		</div>
	</div>
</section>

<template>
	<Alert :variant="variant">
		<component :is="icon" class="h-4 w-4"></component>
		<slot />
	</Alert>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { CircleCheckBig, CircleAlert, type LucideIcon } from "lucide-vue-next";

import { Alert } from "@/components/ui/alert";

const { variant } = defineProps<{
	variant: "success" | "destructive";
}>();

const icon = computed<LucideIcon>(() => {
	switch (variant) {
		case "success":
			return CircleCheckBig;
		case "destructive":
			return CircleAlert;
	}
});
</script>

<script setup lang="ts">
import type { HTMLAttributes, AnchorHTMLAttributes } from "vue";
import { Primitive, type PrimitiveProps } from "radix-vue";
import { Loader2 } from "lucide-vue-next";
import { type ButtonVariants, buttonVariants } from ".";
import { cn } from "@/lib/utils";

interface DefaultProps extends PrimitiveProps {
	variant?: ButtonVariants["variant"];
	size?: ButtonVariants["size"];
	class?: HTMLAttributes["class"];
	id?: HTMLAttributes["id"];
	isLoading?: boolean;
}

interface ButtonProps extends DefaultProps {
	as: "button";
}

interface LinkProps extends DefaultProps {
	as: "a";
	href?: AnchorHTMLAttributes["href"];
}

const props = withDefaults(defineProps<ButtonProps | LinkProps>(), {
	as: "button",
	isLoading: false
});
</script>

<template>
	<Primitive
		:id="props.id"
		:as="as"
		:as-child="asChild"
		:class="cn(buttonVariants({ variant, size }), props.class)"
		:disabled="isLoading"
		:href="href"
	>
		<Loader2 v-if="isLoading" class="mr-2 h-4 w-4 animate-spin" />
		<slot />
		<template v-if="isLoading">...</template>
	</Primitive>
</template>

---
import type { HTMLAttributes, HTMLTag } from "astro/types";
import { Picture, type PictureProps } from ".";

import { cn } from "@/lib/utils";

type Props = PictureProps & {
	tag?: HTMLTag;
	tagAttributes?: HTMLAttributes<"section">;
};

const {
	tag: Element = "section",
	tagAttributes = {},
	class: propsClass,
	role = "presentation",
	...props
} = Astro.props;

const { class: tagClass, ...fallthroughTagAttributes } = tagAttributes;
---

<Element
	class={cn("astro-bg-picture relative overflow-hidden", tagClass)}
	{...fallthroughTagAttributes}
>
	<Picture
		class={cn("absolute inset-0 z-0 h-full w-full object-cover object-top", propsClass)}
		role={role}
		{...props}
	/>
	<slot />
</Element>

<style is:global>
	.astro-bg-picture > :not(picture:first-child),
	.astro-bg-picture > :not(picture:first-child) > * {
		@apply relative z-10;
	}
</style>

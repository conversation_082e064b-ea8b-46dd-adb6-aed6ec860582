{"name": "landing-page-cambridge", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro check --minimumSeverity warning && astro build", "preview": "astro preview", "astro": "astro", "format": "pnpm exec prettier ./src --write"}, "dependencies": {"@astrojs/check": "^0.9.3", "@astrojs/node": "^8.3.4", "@astrojs/partytown": "^2.1.2", "@astrojs/sitemap": "^3.2.0", "@astrojs/tailwind": "^5.1.1", "@astrojs/vue": "^4.5.1", "@astrolib/seo": "^1.0.0-beta.8", "@digi4care/astro-google-tagmanager": "^1.4.0", "@formkit/auto-animate": "^0.8.2", "@vee-validate/zod": "^4.13.2", "@vueuse/core": "^11.1.0", "astro": "^4.15.10", "astro-robots-txt": "^1.0.0", "balloon-css": "^1.2.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "dotenv": "^16.4.5", "embla-carousel-autoplay": "^8.3.0", "embla-carousel-vue": "^8.3.0", "lucide-vue-next": "^0.441.0", "prettier": "^3.3.3", "prettier-plugin-astro": "^0.14.1", "prettier-plugin-tailwindcss": "^0.6.6", "radix-vue": "^1.9.5", "sharp": "^0.33.5", "tailwind-merge": "^2.5.2", "tailwindcss": "^3.4.11", "tailwindcss-animate": "^1.0.7", "typescript": "^5.6.2", "vee-validate": "^4.13.2", "vue": "^3.5.6", "vue-recaptcha-v3": "^2.0.1", "zod": "^3.23.8"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39", "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@types/node": "^22.7.2"}}
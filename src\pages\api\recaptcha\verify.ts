import "dotenv/config";
import type { APIRoute } from "astro";

import { StatusCodes, restResponseSuccess, restResponseError } from "@/lib/restResponse";

export const prerender = false;

type RecaptchaErrorCodes =
	| "missing-input-secret" // The secret parameter is missing.
	| "invalid-input-secret" // The secret parameter is invalid or malformed.
	| "missing-input-response" // The response parameter is missing.
	| "invalid-input-response" // The response parameter is invalid or malformed.
	| "bad-request" // The request is invalid or malformed.
	| "timeout-or-duplicate"; // The response is no longer valid: either is too old or has been used previously.
type VerifyRecaptchaResponse = {
	success: boolean; // whether this request was a valid reCAPTCHA token for your site
	score: number; // the score for this request (0.0 - 1.0)
	action: string; // the action name for this request (important to verify)
	challenge_ts: string; // timestamp of the challenge load (ISO format yyyy-MM-dd'T'HH:mm:ssZZ)
	hostname: string; // the hostname of the site where the reCAPTCHA was solved
	"error-codes"?: RecaptchaErrorCodes; // optional
};
type RequestData = {
	token: string;
	action: string;
};
export type VerifiedRecaptchaResponse = {
	token: string;
	action: string;
	score: number;
};

const secret = process.env.RECAPTCHA_SECRET_KEY;
if (!secret) throw new Error("Environment variable RECAPTCHA_SECRET_KEY is not set");

export const POST: APIRoute = async ({ request }) => {
	if (request.headers.get("Content-Type") !== "application/json") {
		return restResponseError(StatusCodes.BadRequest, "Content-Type must be application/json");
	}

	const requestData: RequestData = await request.json();

	const recaptchaURL = "https://www.google.com/recaptcha/api/siteverify";
	const response = await fetch(recaptchaURL, {
		method: "POST",
		headers: {
			"Content-Type": "application/x-www-form-urlencoded"
		},
		body: new URLSearchParams({
			secret,
			response: requestData.token // The token passed in from the client
		})
	});

	const verifyRecaptchaResponse: VerifyRecaptchaResponse = await response.json();

	if (!verifyRecaptchaResponse.success) {
		return restResponseError(
			StatusCodes.BadRequest,
			verifyRecaptchaResponse["error-codes"] ?? "Invalid reCAPTCHA token"
		);
	}

	if (verifyRecaptchaResponse.action !== requestData.action) {
		return restResponseError(StatusCodes.BadRequest, "Invalid reCAPTCHA action");
	}

	const threshold = 0.5;
	if (verifyRecaptchaResponse.score >= threshold) {
		return restResponseSuccess(StatusCodes.Ok, {
			token: requestData.token,
			action: requestData.action,
			score: verifyRecaptchaResponse.score
		} satisfies VerifiedRecaptchaResponse);
	}

	return restResponseError(StatusCodes.BadRequest, "Invalid reCAPTCHA token");
};

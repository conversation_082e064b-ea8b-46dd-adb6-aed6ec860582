---
import { TextStroke } from "@/components/astro";
import { Image, Picture } from "@/components/astro/assets";

import scLicense1 from "@/assets/studycare-license-1.png";
import scLicense2p1 from "@/assets/studycare-license-2-p1.png";
import scLicense2p2 from "@/assets/studycare-license-2-p2.png";

const licenses = [
	{
		src: scLicense1,
		alt: "Gi<PERSON>y chứng nhận đăng ký doanh nghiệp"
	},
	{
		src: scLicense2p1,
		alt: "<PERSON><PERSON><PERSON><PERSON> phép thành lập trung tâm anh ngữ trang 1"
	},
	{
		src: scLicense2p2,
		alt: "<PERSON><PERSON><PERSON><PERSON> phép thành lập trung tâm anh ngữ trang 1"
	}
];
---

<section data-name="authorized-ged" class="bg-[#045066] py-16">
	<div
		class="container flex flex-col items-center justify-center gap-16 lg:flex-row-reverse lg:gap-24"
	>
		<h2
			class="flex w-full flex-col items-center gap-4 text-center lg:w-3/5"
			data-name="section-right"
		>
			<div class="rounded-xl bg-white">
				<Image src="/site/web-logo.svg" alt="StudyCare Education" width={150} height={106} />
				<span class="sr-only">StudyCare Education</span>
			</div>
			<TextStroke
				as="span"
				text="An Authorized Education Partner"
				class="text-center text-3xl/tight font-bold uppercase lg:text-4xl/tight"
				color="hsl(var(--primary-foreground))"
				strokeColor="hsl(var(--primary))"
				strokeWidth={3}
			/>
			<TextStroke
				as="span"
				text="đơn-vị đối-tác tổ-chức khảo-thí bài kiểm-tra"
				class="text-center text-3xl/tight font-bold uppercase lg:text-4xl/tight"
				color="hsl(var(--primary-foreground))"
				strokeColor="hsl(var(--primary))"
				strokeWidth={3}
			/>
			<div class="rounded-xl bg-white p-4">
				<Image src="/site/ged-logo.svg" alt="GED" width={130} height={74} />
				<span class="sr-only">GED</span>
			</div>
		</h2>
		<div class="flex w-full flex-col items-center gap-4 lg:w-2/5" data-name="section-left">
			<TextStroke
				as="h2"
				text="Cơ-sở pháp-lý / Legal Basis"
				class="text-center text-3xl/tight font-bold uppercase"
				color="hsl(var(--primary-foreground))"
				strokeColor="hsl(var(--primary))"
				strokeWidth={3}
			/>
			<div
				class="flex items-center justify-center rounded-2xl border-2 border-b-4 border-primary-foreground bg-white p-4 shadow-xl"
			>
				{
					licenses.map((license) => (
						<Picture
							src={license.src}
							alt={license.alt}
							widths={[80, 160, 100, 200, 300, 400]}
							sizes="(max-width: 425px) 80px, (max-width: 768px) 200px, (max-width: 1024px) 100px, 150px"
						/>
					))
				}
			</div>
		</div>
	</div>
</section>

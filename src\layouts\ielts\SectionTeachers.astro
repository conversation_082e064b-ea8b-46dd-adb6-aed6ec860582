---
import { Picture, PictureBackground } from "@/components/astro/assets";
import { TextStroke } from "@/components/astro";

import { CircleCheckBig } from "lucide-vue-next";
import { Button } from "@/components/ui/button";
import { PopupContactForm } from "@/components/custom/PopupContactForm";
import { TeachersTabs, teachers } from "@/components/custom/TeachersTabs";

import teachersBgImg from "@/assets/teachers-bg.png";
---

<PictureBackground
	src={teachersBgImg}
	alt="Ảnh nền bầu trời đêm"
	tagAttributes={{
		"data-name": "teachers",
		class: "bg-[#045066] py-16"
	}}
>
	<div
		class="container flex flex-col items-center justify-center gap-16 lg:flex-row lg:justify-between lg:gap-24"
		data-name="section-content"
	>
		<div data-name="section-left" class="w-full lg:w-1/2 xl:w-1/3">
			<h2
				class="mb-6 flex flex-col items-center justify-center text-center text-[1.7rem]/snug font-bold uppercase sm:text-3xl lg:text-4xl/snug"
			>
				<TextStroke
					as="span"
					text="Đơn vị tiên phong đào tạo Cambridge"
					class="inline-block"
					color="white"
					strokeColor="hsl(var(--primary))"
					strokeWidth={3}
				/>
				<TextStroke
					as="span"
					text="từ năm 2011"
					class="inline-block"
					color="hsl(var(--primary-foreground))"
					strokeColor="hsl(var(--primary))"
					strokeWidth={3}
				/>
			</h2>
			<div
				data-name="teachers-content"
				class="mx-auto w-full max-w-2xl rounded-2xl border-2 border-b-4 border-primary bg-white p-6 text-base shadow-xl lg:text-lg"
			>
				<h3 class="mb-2 text-center text-2xl font-bold">
					<span class="text-secondary">StudyCare</span>
					<span class="text-primary">Education</span><br />
					<TextStroke
						as="span"
						class="inline-block uppercase"
						text="lựa chọn an tâm"
						color="hsl(var(--primary-foreground))"
						strokeColor="hsl(var(--primary))"
					/>
				</h3>
				<ul data-name="teachers-content-list" class="mb-2 flex flex-col gap-2">
					<li>
						<CircleCheckBig size={24} color="#16a34a" />
						<span>
							Đơn vị <strong>tiên phong đào tạo chương trình Cambridge</strong> tại Việt Nam.
						</span>
					</li>
					<li>
						<CircleCheckBig size={24} color="#16a34a" />
						<span>
							Đội ngũ giáo viên, giảng viên <strong>chuyên môn cao, giàu kinh nghiệm</strong>.
						</span>
					</li>
					<li>
						<CircleCheckBig size={24} color="#16a34a" />
						<span>
							Giảng dạy đầy đủ <strong>tất cả các môn học</strong> trong chương trình Cambridge.
						</span>
					</li>
					<li>
						<CircleCheckBig size={24} color="#16a34a" />
						<span>Phương pháp học <strong>tương tác, cá nhân hóa</strong> cho từng học sinh.</span>
					</li>
					<li>
						<CircleCheckBig size={24} color="#16a34a" />
						<span>
							Theo sát học viên hoàn thành các <strong>bài tập trên LMS</strong> với
							<strong>kết quả tối ưu</strong>.
						</span>
					</li>
				</ul>
				<PopupContactForm
					formID="1FAIpQLSeXo2dqpMX8_3FmlUPitmCmIZTdIsdf_jMO5s1Pi5ZyLRlTIg"
					id="teachers-contact-form"
					client:load
				>
					<Button as="button" class="w-full" id="teachers-contact-form-popup">
						Đăng ký học thử miễn phí
					</Button>
				</PopupContactForm>
			</div>
		</div>
		<div data-name="section-right" class="w-full lg:w-1/2 xl:w-2/3">
			<div class="flex justify-center">
				<TextStroke
					as="h2"
					text="Đội ngũ giảng viên ưu tú"
					class="mb-6 text-center text-[1.7rem] font-bold uppercase sm:text-3xl md:text-4xl"
					color="white"
					strokeColor="hsl(var(--primary))"
					strokeWidth={3}
				/>
			</div>
			<TeachersTabs client:load>
				<Picture slot={teachers[0].slot} src={teachers[0].img} alt={teachers[0].label} />
				<Picture slot={teachers[1].slot} src={teachers[1].img} alt={teachers[1].label} />
				<Picture slot={teachers[2].slot} src={teachers[2].img} alt={teachers[2].label} />
				<Picture slot={teachers[3].slot} src={teachers[3].img} alt={teachers[3].label} />
				<Picture slot={teachers[4].slot} src={teachers[4].img} alt={teachers[4].label} />
				<Picture slot={teachers[5].slot} src={teachers[5].img} alt={teachers[5].label} />
				<Picture slot={teachers[6].slot} src={teachers[6].img} alt={teachers[6].label} />
				<Picture slot={teachers[7].slot} src={teachers[7].img} alt={teachers[7].label} />
				<Picture slot={teachers[8].slot} src={teachers[8].img} alt={teachers[8].label} />
				<Picture slot={teachers[9].slot} src={teachers[9].img} alt={teachers[9].label} />
				<Picture slot={teachers[10].slot} src={teachers[10].img} alt={teachers[10].label} />
				<Picture slot={teachers[11].slot} src={teachers[11].img} alt={teachers[11].label} />
			</TeachersTabs>
		</div>
	</div>
</PictureBackground>

<style>
	[data-name="teachers-content-list"] li {
		@apply flex items-center gap-4;
	}
	[data-name="teachers-content-list"] svg {
		@apply shrink-0;
	}
</style>

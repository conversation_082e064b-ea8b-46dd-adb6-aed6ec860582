import { useForm as _useForm } from "vee-validate";
import { toTypedSchema } from "@vee-validate/zod";
import * as z from "zod";

import type { RestResponse } from "@/lib/restResponse";

const formFields = {
	fullName: {
		name: "fullName",
		type: "text",
		label: "Họ và tên <PERSON> sinh",
		placeholder: "Nguyễn Văn A"
	},
	phone: {
		name: "phone",
		type: "text",
		label: "Số điện thoại Phụ huynh",
		placeholder: "0983531175"
	}
} satisfies Record<
	string,
	{
		name: string;
		type: string;
		label: string;
		placeholder?: string;
	}
>;

const formSchema = toTypedSchema(
	z.object({
		fullName: z
			.string({ message: "Vui lòng điền Họ và tên!" })
			.min(3, "Vui lòng điền đầy đủ Họ và tên!")
			.max(50, "<PERSON><PERSON> và tên không thể dài hơn 50 ký tự!"),
		phone: z
			.string({ message: "<PERSON><PERSON> lòng điền số điện thoại" })
			.startsWith("0", "Số điện thoại phải bắt đầu bằng 0")
			.min(9, "Số điện thoại không thể ít hơn 9 chữ số")
			.max(11, "Số điện thoại không thể dài hơn 11 chữ số")
			.refine((val) => /^0\d{9,11}$/.test(val), {
				message: "Vui lòng điền một Số điện thoại hợp lệ!"
			})
	})
);

const useForm = () =>
	_useForm({
		validationSchema: formSchema
	});

const useFormSubmit = async (
	formID: string,
	values: Record<string, string>
): Promise<RestResponse<string>> => {
	const params = new URLSearchParams();
	params.set("form_id", formID);

	const submitUrl = `/api/ggform/submit?${params.toString()}`;
	const response = await fetch(submitUrl, {
		method: "POST",
		headers: {
			"Content-Type": "application/json"
		},
		body: JSON.stringify(values)
	});

	return await response.json();
};

export { useForm, useFormSubmit, formFields };

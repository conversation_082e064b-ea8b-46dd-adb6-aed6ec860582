export { useReCaptcha } from "vue-recaptcha-v3";
import type { IReCaptchaComposition } from "vue-recaptcha-v3";

import type { RestResponse } from "@/lib/restResponse";
import type { VerifiedRecaptchaResponse } from "@/pages/api/recaptcha/verify";

type VerifiedRecaptcha = RestResponse<VerifiedRecaptchaResponse>;

export const useVerifyRecaptcha = async (
	ReCaptcha: IReCaptchaComposition | undefined,
	action: string
): Promise<VerifiedRecaptcha> => {
	if (ReCaptcha === undefined) throw new Error("ReCaptcha is not loaded");

	const { executeRecaptcha, recaptchaLoaded } = ReCaptcha;
	// Wait until recaptcha has been loaded.
	await recaptchaLoaded();
	// Execute reCAPTCHA with action.
	const token = await executeRecaptcha(action);
	// Verify the received token.
	const response = await fetch("api/recaptcha/verify", {
		method: "POST",
		headers: {
			"Content-Type": "application/json"
		},
		body: JSON.stringify({ token, action })
	});

	return await response.json();
};

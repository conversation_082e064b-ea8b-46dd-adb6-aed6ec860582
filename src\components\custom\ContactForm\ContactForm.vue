<template>
	<form :id="props.id" class="flex w-full flex-col gap-4" @submit="onSubmit">
		<FormField v-slot="{ componentField }" :name="formFields.fullName.name">
			<FormItem v-auto-animate>
				<FormLabel class="text-md">{{ formFields.fullName.label }}</FormLabel>
				<FormControl>
					<Input
						class="focus-visible:ring-blue-500 focus-visible:ring-opacity-20 focus-visible:ring-offset-0"
						:type="formFields.fullName.type"
						:placeholder="formFields.fullName.placeholder"
						v-bind="componentField"
					/>
				</FormControl>
				<FormMessage />
			</FormItem>
		</FormField>
		<FormField v-slot="{ componentField }" :name="formFields.phone.name">
			<FormItem v-auto-animate>
				<FormLabel class="text-md">{{ formFields.phone.label }}</FormLabel>
				<FormControl>
					<Input
						class="focus-visible:ring-blue-500 focus-visible:ring-opacity-20 focus-visible:ring-offset-0"
						:type="formFields.phone.type"
						:placeholder="formFields.phone.placeholder"
						v-bind="componentField"
					/>
				</FormControl>
				<FormMessage />
			</FormItem>
		</FormField>
		<Button
			as="button"
			type="submit"
			size="lg"
			:is-loading="isButtonLoading"
			class="mx-auto uppercase"
		>
			Đăng ký miễn phí
		</Button>
	</form>
	<div v-auto-animate>
		<Alert :variant="confirmationVariant" v-if="isShowingConfirmation" class="mt-4">
			<AlertTitle>{{ confirmationTitle }}</AlertTitle>
			<AlertDescription>{{ confirmationDescription }}</AlertDescription>
		</Alert>
	</div>
</template>

<script setup lang="ts">
import { ref, type HTMLAttributes } from "vue";
import { vAutoAnimate } from "@formkit/auto-animate";
import { useReCaptcha, useVerifyRecaptcha } from "@/lib/recaptcha";
import { sleep } from "@/lib/features";

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/custom/Alert";

import { useForm, formFields, useFormSubmit } from "./form";

interface Props {
	formID: string;
	id?: HTMLAttributes["id"];
}

const props = defineProps<Props>();

const isButtonLoading = ref(false);
const isFailedRecaptcha = ref(false);

type ConfirmationVariant = "success" | "destructive";
const isShowingConfirmation = ref(false);
const confirmationVariant = ref<ConfirmationVariant>("success");
const confirmationTitle = ref("Đăng ký thành công!");
const confirmationDescription = ref(
	"StudyCare Education sẽ liên hệ với bạn trong thời gian sớm nhất."
);

const showConfirmation = (variant: ConfirmationVariant, title?: string, message?: string) => {
	isShowingConfirmation.value = true;

	confirmationVariant.value = variant;
	if (title) confirmationTitle.value = title;
	if (message) confirmationDescription.value = message;
};

const ReCaptcha = useReCaptcha();

const { handleSubmit } = useForm();
const onSubmit = handleSubmit(async (values, { resetForm }) => {
	isButtonLoading.value = true;
	isShowingConfirmation.value = false;
	await sleep(500);

	// Verify the reCAPTCHA
	if (isFailedRecaptcha.value) {
		isButtonLoading.value = false;
		console.error("Previously failed ReCaptcha Verification");

		return showConfirmation(
			"destructive",
			"Đã có lỗi xảy ra!",
			"Bạn vui lòng tải lại trang hoặc liên hệ trực tiếp với StudyCare Education để được hỗ trợ ngay."
		);
	}
	const recaptchaAction = "contact_form_submit";
	const verifiedRecaptcha = await useVerifyRecaptcha(ReCaptcha, recaptchaAction);

	if (!verifiedRecaptcha.success) {
		isButtonLoading.value = false;
		if (!isFailedRecaptcha.value) isFailedRecaptcha.value = true;
		console.error(verifiedRecaptcha.message);

		return showConfirmation(
			"destructive",
			"Đã có lỗi xảy ra!",
			"Bạn vui lòng tải lại trang hoặc liên hệ trực tiếp với StudyCare Education để được hỗ trợ ngay."
		);
	}

	// ReCaptcha verified, submit form
	const formSubmitted = await useFormSubmit(props.formID, values);

	isButtonLoading.value = false;
	// @ts-ignore
	if (!formSubmitted.success) {
		console.error(formSubmitted.message);
		return showConfirmation(
			"destructive",
			"Đã có lỗi xảy ra!",
			"Bạn vui lòng tải lại trang hoặc liên hệ trực tiếp với StudyCare Education để được hỗ trợ ngay."
		);
	}

	resetForm();
	return showConfirmation("success");
});
</script>

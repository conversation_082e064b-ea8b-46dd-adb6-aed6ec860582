export type RestResponseSuccess<T> = {
	success: true;
	data: T;
};

export type RestResponseError = {
	success: false;
	message: string;
};

export type RestResponse<T> = RestResponseSuccess<T> | RestResponseError;

export enum StatusCodes {
	Continue = 100,
	SwitchingProtocols = 101,
	Processing = 102,
	EarlyHints = 103,
	Ok = 200,
	Created = 201,
	Accepted = 202,
	NonAuthoritativeInformation = 203,
	NoContent = 204,
	ResetContent = 205,
	PartialContent = 206,
	MultiStatus = 207,
	MultipleChoices = 300,
	MovedPermanently = 301,
	MovedTemporarily = 302,
	SeeOther = 303,
	NotModified = 304,
	UseProxy = 305,
	TemporaryRedirect = 307,
	PermanentRedirect = 308,
	BadRequest = 400,
	Unauthorized = 401,
	PaymentRequired = 402,
	Forbidden = 403,
	NotFound = 404,
	MethodNotAllowed = 405,
	NotAcceptable = 406,
	ProxyAuthenticationRequired = 407,
	RequestTimeout = 408,
	Conflict = 409,
	Gone = 410,
	LengthRequired = 411,
	PreconditionFailed = 412,
	RequestTooLong = 413,
	RequestUriTooLong = 414,
	UnsupportedMediaType = 415,
	RequestedRangeNotSatisfiable = 416,
	ExpectationFailed = 417,
	ImATeapot = 418,
	InsufficientSpaceOnResource = 419,
	MethodFailure = 420,
	MisdirectedRequest = 421,
	UnprocessableEntity = 422,
	Locked = 423,
	FailedDependency = 424,
	UpgradeRequired = 426,
	PreconditionRequired = 428,
	TooManyRequests = 429,
	RequestHeaderFieldsTooLarge = 431,
	UnavailableForLegalReasons = 451,
	InternalServerError = 500,
	NotImplemented = 501,
	BadGateway = 502,
	ServiceUnavailable = 503,
	GatewayTimeout = 504,
	HttpVersionNotSupported = 505,
	InsufficientStorage = 507,
	NetworkAuthenticationRequired = 511
}

export const restResponseSuccess = <T>(
	statusCode: StatusCodes,
	data: T,
	init?: ResponseInit
): Response => {
	if (statusCode < 200 || statusCode >= 300)
		throw new Error(`A success response can't have a status code: ${statusCode}`);

	return new Response(JSON.stringify({ success: true, data }), {
		...init,
		status: statusCode
	});
};

export const restResponseError = (
	statusCode: StatusCodes,
	message: string,
	init?: ResponseInit
): Response => {
	if (statusCode >= 200 && statusCode < 300)
		throw new Error(`An error response can't have a status code: ${statusCode}`);

	return new Response(JSON.stringify({ success: false, message }), {
		...init,
		status: statusCode
	});
};
